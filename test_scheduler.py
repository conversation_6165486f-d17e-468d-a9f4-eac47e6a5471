#!/usr/bin/env python3
"""
测试脚本 - 用于测试调度器的各种功能
"""

import subprocess
import time
import sys
from pathlib import Path

def test_basic_functionality():
    """测试基本功能"""
    print("=== 测试基本功能 ===")
    
    # 创建配置文件
    print("1. 创建配置文件...")
    result = subprocess.run([
        sys.executable, "schedule_execute_process_daily.py", "--create-config"
    ], capture_output=True, text=True)
    
    if result.returncode == 0:
        print("✓ 配置文件创建成功")
    else:
        print(f"✗ 配置文件创建失败: {result.stderr}")
        return False
    
    return True

def test_script_execution():
    """测试脚本执行（短时间测试）"""
    print("\n=== 测试脚本执行 ===")
    
    # 启动调度器，每1分钟执行一次示例脚本
    print("2. 启动调度器（将运行2分钟进行测试）...")
    
    try:
        proc = subprocess.Popen([
            sys.executable, "schedule_execute_process_daily.py", 
            "example_script.py", "1", "scheduler_config.json"
        ])
        
        print("✓ 调度器已启动")
        print("等待120秒以观察执行情况...")
        
        # 等待2分钟
        time.sleep(120)
        
        # 终止调度器
        proc.terminate()
        proc.wait(timeout=10)
        
        print("✓ 调度器已停止")
        
        # 检查日志文件
        logs_dir = Path("logs")
        if logs_dir.exists():
            log_files = list(logs_dir.glob("*.log"))
            print(f"✓ 生成了 {len(log_files)} 个日志文件:")
            for log_file in log_files:
                print(f"  - {log_file}")
                
            # 显示最新的脚本日志内容
            script_logs = [f for f in log_files if "example_script" in f.name]
            if script_logs:
                latest_log = max(script_logs, key=lambda x: x.stat().st_mtime)
                print(f"\n最新脚本日志内容 ({latest_log}):")
                print("-" * 50)
                with open(latest_log, 'r', encoding='utf-8') as f:
                    content = f.read()
                    print(content[:500] + "..." if len(content) > 500 else content)
                print("-" * 50)
        else:
            print("✗ 未找到日志目录")
            return False
            
    except subprocess.TimeoutExpired:
        print("✗ 调度器终止超时")
        proc.kill()
        return False
    except Exception as e:
        print(f"✗ 测试过程中出现错误: {e}")
        return False
    
    return True

def test_error_handling():
    """测试错误处理"""
    print("\n=== 测试错误处理 ===")
    
    # 测试不存在的脚本
    print("3. 测试不存在的脚本...")
    result = subprocess.run([
        sys.executable, "schedule_execute_process_daily.py", 
        "nonexistent_script.py", "1"
    ], capture_output=True, text=True)
    
    if result.returncode != 0:
        print("✓ 正确处理了不存在的脚本")
    else:
        print("✗ 未正确处理不存在的脚本")
        return False
    
    return True

def show_usage_examples():
    """显示使用示例"""
    print("\n=== 使用示例 ===")
    print("以下是一些使用示例:")
    print()
    print("1. 每小时执行一次脚本:")
    print("   python schedule_execute_process_daily.py example_script.py 60")
    print()
    print("2. 每天执行一次脚本:")
    print("   python schedule_execute_process_daily.py example_script.py 1440")
    print()
    print("3. 使用配置文件:")
    print("   python schedule_execute_process_daily.py example_script.py 30 scheduler_config.json")
    print()
    print("4. 查看帮助:")
    print("   python schedule_execute_process_daily.py")

def main():
    """主测试函数"""
    print("Python 脚本调度器测试")
    print("=" * 40)
    
    # 检查必要文件
    required_files = ["schedule_execute_process_daily.py", "example_script.py"]
    for file in required_files:
        if not Path(file).exists():
            print(f"✗ 缺少必要文件: {file}")
            return 1
    
    print("✓ 所有必要文件都存在")
    
    # 运行测试
    tests = [
        test_basic_functionality,
        test_script_execution,
        test_error_handling
    ]
    
    passed = 0
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"✗ 测试失败: {test.__name__}")
        except Exception as e:
            print(f"✗ 测试异常: {test.__name__} - {e}")
    
    print(f"\n测试结果: {passed}/{len(tests)} 通过")
    
    if passed == len(tests):
        print("🎉 所有测试通过!")
        show_usage_examples()
        return 0
    else:
        print("❌ 部分测试失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())
