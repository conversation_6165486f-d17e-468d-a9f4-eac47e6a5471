import subprocess
import sys
import time
import signal
from datetime import datetime
from pathlib import Path
import logging
from logging.handlers import RotatingFileHandler
import json

class ScheduleExecutor:
    def __init__(self, program_path, exec_interval_minutes, config_file=None, show_output=True):
        self.program_path = program_path
        self.exec_interval_minutes = int(exec_interval_minutes)
        self.check_interval = 60  # 检查脚本状态的时间间隔（秒）
        self.current_process = None
        self.should_stop = False
        self.show_output = show_output  # 是否在终端显示实时输出

        # 创建日志目录
        self.log_dir = Path("logs")
        self.log_dir.mkdir(exist_ok=True)

        # 设置日志
        self._setup_logging()

        # 加载配置
        if config_file and Path(config_file).exists():
            self._load_config(config_file)

        # 设置信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

    def _setup_logging(self):
        """设置日志配置"""
        # 调度器日志
        scheduler_log_file = self.log_dir / f"scheduler_{datetime.now().strftime('%Y%m%d')}.log"

        # 创建logger
        self.logger = logging.getLogger('scheduler')
        self.logger.setLevel(logging.INFO)

        # 创建文件处理器（带轮转）
        file_handler = RotatingFileHandler(
            scheduler_log_file,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5
        )
        file_handler.setLevel(logging.INFO)

        # 创建控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)

        # 创建格式器
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)

        # 添加处理器
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)

    def _load_config(self, config_file):
        """加载配置文件"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                self.check_interval = config.get('check_interval', self.check_interval)
                self.logger.info(f"Loaded configuration from {config_file}")
        except Exception as e:
            self.logger.warning(f"Failed to load config file {config_file}: {e}")

    def _signal_handler(self, signum, _frame):
        """信号处理器"""
        self.logger.info(f"Received signal {signum}, shutting down gracefully...")
        self.should_stop = True

    def _get_script_log_file(self):
        """获取被调用脚本的日志文件路径"""
        script_name = Path(self.program_path).stem
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        return self.log_dir / f"{script_name}_{timestamp}.log"

    def _run_script(self):
        """运行脚本并捕获输出"""
        try:
            script_log_file = self._get_script_log_file()

            if self.show_output:
                # 实时显示输出模式
                proc = subprocess.Popen(
                    [sys.executable, self.program_path],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.STDOUT,
                    universal_newlines=True,
                    bufsize=1  # 行缓冲
                )

                self.logger.info(f"Started script: {self.program_path} with PID {proc.pid}")
                self.logger.info(f"Script output will be saved to: {script_log_file}")
                self.logger.info("Real-time output enabled - script output will be shown below:")
                print(f"\n{'='*60}")
                print(f"SCRIPT OUTPUT START - {self.program_path}")
                print(f"{'='*60}")

                return proc, script_log_file
            else:
                # 静默模式，只保存到日志文件
                with open(script_log_file, 'w', encoding='utf-8') as log_file:
                    proc = subprocess.Popen(
                        [sys.executable, self.program_path],
                        stdout=log_file,
                        stderr=subprocess.STDOUT,
                        universal_newlines=True,
                        bufsize=1
                    )

                    self.logger.info(f"Started script: {self.program_path} with PID {proc.pid}")
                    self.logger.info(f"Script output will be saved to: {script_log_file}")

                    return proc, script_log_file

        except Exception as e:
            self.logger.error(f"Failed to start script: {self.program_path}. Error: {e}", exc_info=True)
            raise

    def _monitor_script_output(self, proc, script_log_file):
        """监控脚本输出并实时显示"""
        if not self.show_output:
            return

        try:
            with open(script_log_file, 'w', encoding='utf-8') as log_file:
                while True:
                    output = proc.stdout.readline()
                    if output == '' and proc.poll() is not None:
                        break
                    if output:
                        # 同时显示在终端和保存到文件
                        print(output.rstrip())
                        log_file.write(output)
                        log_file.flush()

                # 确保获取剩余输出
                remaining_output = proc.stdout.read()
                if remaining_output:
                    print(remaining_output.rstrip())
                    log_file.write(remaining_output)

        except Exception as e:
            self.logger.error(f"Error monitoring script output: {e}")
        finally:
            print(f"{'='*60}")
            print(f"SCRIPT OUTPUT END")
            print(f"{'='*60}\n")

    def _terminate_process(self, proc, timeout=10):
        """优雅地终止进程"""
        if proc and proc.poll() is None:
            self.logger.warning(f"Terminating process {proc.pid}")
            proc.terminate()

            try:
                proc.wait(timeout=timeout)
                self.logger.info(f"Process {proc.pid} terminated gracefully")
            except subprocess.TimeoutExpired:
                self.logger.warning(f"Process {proc.pid} did not terminate gracefully, killing it")
                proc.kill()
                proc.wait()
                self.logger.warning(f"Process {proc.pid} killed")

    def run(self):
        """主运行循环"""
        self.logger.info(f"Starting scheduler for script: {self.program_path}")
        self.logger.info(f"Execution interval: {self.exec_interval_minutes} minutes")

        while not self.should_stop:
            start_time = datetime.now()

            # 终止之前的进程（如果还在运行）
            if self.current_process and self.current_process.poll() is None:
                self.logger.warning("Previous script is still running. Terminating it.")
                self._terminate_process(self.current_process)

            # 启动新脚本
            try:
                self.current_process, script_log_file = self._run_script()

                # 如果启用实时输出，启动输出监控线程
                if self.show_output:
                    import threading
                    output_thread = threading.Thread(
                        target=self._monitor_script_output,
                        args=(self.current_process, script_log_file),
                        daemon=True
                    )
                    output_thread.start()

                # 监控脚本执行
                program_interval_seconds = self.exec_interval_minutes * 60

                while not self.should_stop and (datetime.now() - start_time).total_seconds() < program_interval_seconds:
                    time.sleep(self.check_interval)

                    if self.current_process.poll() is not None:  # 脚本已完成
                        return_code = self.current_process.returncode
                        if return_code == 0:
                            self.logger.info(f"Script completed successfully. Return code: {return_code}")
                        else:
                            self.logger.error(f"Script completed with error. Return code: {return_code}")

                        # 等待输出线程完成
                        if self.show_output and output_thread.is_alive():
                            output_thread.join(timeout=5)

                        # 计算剩余等待时间
                        elapsed_time = (datetime.now() - start_time).total_seconds()
                        remaining_time = program_interval_seconds - elapsed_time

                        if remaining_time > 0 and not self.should_stop:
                            self.logger.info(f"Waiting for {remaining_time:.0f} seconds before next execution.")
                            # 分段等待，以便能够响应停止信号
                            while remaining_time > 0 and not self.should_stop:
                                sleep_time = min(self.check_interval, remaining_time)
                                time.sleep(sleep_time)
                                remaining_time -= sleep_time
                        break

            except Exception as e:
                self.logger.error(f"Error during script execution: {e}", exc_info=True)
                # 等待一段时间后重试
                if not self.should_stop:
                    self.logger.info("Waiting 5 minutes before retry...")
                    time.sleep(300)

        # 清理
        if self.current_process and self.current_process.poll() is None:
            self.logger.info("Shutting down, terminating current process...")
            self._terminate_process(self.current_process)

        self.logger.info("Scheduler stopped.")

def create_sample_config():
    """创建示例配置文件"""
    config = {
        "check_interval": 60,
        "log_retention_days": 7,
        "max_log_size_mb": 10,
        "description": "Configuration file for schedule executor"
    }

    config_file = Path("scheduler_config.json")
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)

    print(f"Sample configuration file created: {config_file}")

if __name__ == '__main__':
    # 创建配置文件
    if len(sys.argv) == 2 and sys.argv[1] == '--create-config':
        create_sample_config()
        sys.exit(0)

    if len(sys.argv) < 3:
        print("Usage: python schedule_execute_process_daily.py <path_to_program> <program_interval_minutes> [config_file] [--silent]")
        print("       python schedule_execute_process_daily.py --create-config")
        print("\nOptions:")
        print("  --silent    Run in silent mode (no real-time output display)")
        print("\nExamples:")
        print("  python schedule_execute_process_daily.py my_script.py 60")
        print("  python schedule_execute_process_daily.py my_script.py 1440 scheduler_config.json")
        print("  python schedule_execute_process_daily.py my_script.py 60 --silent")
        print("  python schedule_execute_process_daily.py --create-config")
        sys.exit(1)

    program_path = sys.argv[1]
    exec_interval_minutes = sys.argv[2]

    # 解析可选参数
    config_file = None
    show_output = True  # 默认显示实时输出

    for arg in sys.argv[3:]:
        if arg == '--silent':
            show_output = False
        elif not arg.startswith('--'):
            config_file = arg

    # 验证程序路径
    if not Path(program_path).exists():
        print(f"Error: Program file '{program_path}' does not exist.")
        sys.exit(1)

    try:
        scheduler = ScheduleExecutor(program_path, exec_interval_minutes, config_file)
        scheduler.run()
    except KeyboardInterrupt:
        print("\nReceived interrupt signal, shutting down...")
    except Exception as e:
        print(f"Process error: {e}")
        sys.exit(1)
