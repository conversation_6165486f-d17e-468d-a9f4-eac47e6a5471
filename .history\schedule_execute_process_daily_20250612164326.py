import subprocess
import sys
import time
import signal
import os
from datetime import datetime
from pathlib import Path
import logging
from logging.handlers import RotatingFileHandler
import json
import threading

class ScheduleExecutor:
    def __init__(self, program_path, exec_interval_minutes, config_file=None):
        self.program_path = program_path
        self.exec_interval_minutes = int(exec_interval_minutes)
        self.check_interval = 60  # 检查脚本状态的时间间隔（秒）
        self.current_process = None
        self.should_stop = False

        # 创建日志目录
        self.log_dir = Path("logs")
        self.log_dir.mkdir(exist_ok=True)

        # 设置日志
        self._setup_logging()

        # 加载配置
        if config_file and Path(config_file).exists():
            self._load_config(config_file)

        # 设置信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

    def _setup_logging(self):
        """设置日志配置"""
        # 调度器日志
        scheduler_log_file = self.log_dir / f"scheduler_{datetime.now().strftime('%Y%m%d')}.log"

        # 创建logger
        self.logger = logging.getLogger('scheduler')
        self.logger.setLevel(logging.INFO)

        # 创建文件处理器（带轮转）
        file_handler = RotatingFileHandler(
            scheduler_log_file,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5
        )
        file_handler.setLevel(logging.INFO)

        # 创建控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)

        # 创建格式器
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)

        # 添加处理器
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)

    def _load_config(self, config_file):
        """加载配置文件"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                self.check_interval = config.get('check_interval', self.check_interval)
                self.logger.info(f"Loaded configuration from {config_file}")
        except Exception as e:
            self.logger.warning(f"Failed to load config file {config_file}: {e}")

    def _signal_handler(self, signum, frame):
        """信号处理器"""
        self.logger.info(f"Received signal {signum}, shutting down gracefully...")
        self.should_stop = True

    def _get_script_log_file(self):
        """获取被调用脚本的日志文件路径"""
        script_name = Path(self.program_path).stem
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        return self.log_dir / f"{script_name}_{timestamp}.log"

    def _run_script(self):
        """运行脚本并捕获输出"""
        try:
            script_log_file = self._get_script_log_file()

            # 打开日志文件用于写入脚本输出
            with open(script_log_file, 'w', encoding='utf-8') as log_file:
                proc = subprocess.Popen(
                    [sys.executable, self.program_path],
                    stdout=log_file,
                    stderr=subprocess.STDOUT,  # 将stderr重定向到stdout
                    universal_newlines=True,
                    bufsize=1  # 行缓冲
                )

                self.logger.info(f"Started script: {self.program_path} with PID {proc.pid}")
                self.logger.info(f"Script output will be saved to: {script_log_file}")

                return proc, script_log_file

        except Exception as e:
            self.logger.error(f"Failed to start script: {self.program_path}. Error: {e}", exc_info=True)
            raise

    def _terminate_process(self, proc, timeout=10):
        """优雅地终止进程"""
        if proc and proc.poll() is None:
            self.logger.warning(f"Terminating process {proc.pid}")
            proc.terminate()

            try:
                proc.wait(timeout=timeout)
                self.logger.info(f"Process {proc.pid} terminated gracefully")
            except subprocess.TimeoutExpired:
                self.logger.warning(f"Process {proc.pid} did not terminate gracefully, killing it")
                proc.kill()
                proc.wait()
                self.logger.warning(f"Process {proc.pid} killed")

    def run(self):
        """主运行循环"""
        self.logger.info(f"Starting scheduler for script: {self.program_path}")
        self.logger.info(f"Execution interval: {self.exec_interval_minutes} minutes")

        while not self.should_stop:
            start_time = datetime.now()

            # 终止之前的进程（如果还在运行）
            if self.current_process and self.current_process.poll() is None:
                self.logger.warning("Previous script is still running. Terminating it.")
                self._terminate_process(self.current_process)

            # 启动新脚本
            try:
                self.current_process, script_log_file = self._run_script()

                # 监控脚本执行
                program_interval_seconds = self.exec_interval_minutes * 60

                while not self.should_stop and (datetime.now() - start_time).total_seconds() < program_interval_seconds:
                    time.sleep(self.check_interval)

                    if self.current_process.poll() is not None:  # 脚本已完成
                        return_code = self.current_process.returncode
                        if return_code == 0:
                            self.logger.info(f"Script completed successfully. Return code: {return_code}")
                        else:
                            self.logger.error(f"Script completed with error. Return code: {return_code}")

                        # 计算剩余等待时间
                        elapsed_time = (datetime.now() - start_time).total_seconds()
                        remaining_time = program_interval_seconds - elapsed_time

                        if remaining_time > 0 and not self.should_stop:
                            self.logger.info(f"Waiting for {remaining_time:.0f} seconds before next execution.")
                            # 分段等待，以便能够响应停止信号
                            while remaining_time > 0 and not self.should_stop:
                                sleep_time = min(self.check_interval, remaining_time)
                                time.sleep(sleep_time)
                                remaining_time -= sleep_time
                        break

            except Exception as e:
                self.logger.error(f"Error during script execution: {e}", exc_info=True)
                # 等待一段时间后重试
                if not self.should_stop:
                    self.logger.info("Waiting 5 minutes before retry...")
                    time.sleep(300)

        # 清理
        if self.current_process and self.current_process.poll() is None:
            self.logger.info("Shutting down, terminating current process...")
            self._terminate_process(self.current_process)

        self.logger.info("Scheduler stopped.")

if __name__ == '__main__':
    if len(sys.argv) != 3:
        print("Usage: python schedule_execute_process.py <path_to_program> <program_interval minutes>")
        logging.error("Invalid arguments. Usage: python schedule_execute_process.py <path_to_program> <program_interval minutes>")
        sys.exit(1)

    program_path = sys.argv[1]
    global EXEC_INTERVAL
    EXEC_INTERVAL = sys.argv[2]

    try:
        main(program_path)
    except Exception as e:
        logging.error(f"Process error: {e}", exc_info=True)
        sys.exit(1)
