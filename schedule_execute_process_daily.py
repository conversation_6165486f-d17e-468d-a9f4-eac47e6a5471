import subprocess
import sys
import time
from datetime import datetime
import logging

log_filename = f"log_schedule_execute_process_monitor_{datetime.now().strftime('%Y%m%d')}.txt"
logging.basicConfig(filename=log_filename, level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# 配置
CHECK_INTERVAL = 60  # 检查脚本状态的时间间隔（秒）
#EXEC_INTERVAL = 24 * 60 * 60  # 脚本执行的时间间隔（秒），即1天

def run_script(program_path):
    try:
        proc = subprocess.Popen([sys.executable, program_path])
        logging.info(f"Started script: {program_path} with PID {proc.pid}")
        return proc
    except Exception as e:
        logging.error(f"Failed to start script: {program_path}. Error: {e}", exc_info=True)
        raise

def main(program_path):
    proc = None
    while True:
        start_time = datetime.now()

        if proc and proc.poll() is None:  # 如果上一个脚本还在运行
            logging.warning(f"Previous script is still running. Terminating it: {datetime.now()}")
            proc.terminate()
            time.sleep(5)
            if proc.poll() is None:
                proc.kill()  # 强制杀死进程
                logging.warning(f"Previous script killed: {datetime.now()}")

        proc = run_script(program_path)  # 启动新脚本
        program_interval = int(EXEC_INTERVAL) * 60
        while (datetime.now() - start_time).total_seconds() < program_interval:
            time.sleep(CHECK_INTERVAL)
            if proc.poll() is not None:  # 脚本已完成
                logging.info(f"Script completed: {datetime.now()}. Waiting for next execution.")
                # 计算剩余时间并等待
                remaining_time = program_interval - (datetime.now() - start_time).total_seconds()
                if remaining_time > 0:
                    logging.info(f"Waiting for {remaining_time} seconds before next execution.")
                    time.sleep(int(remaining_time))
                break

        # 这里没有 else，因为如果脚本在一天内完成，我们只需等待到下一个执行周期

if __name__ == '__main__':
    if len(sys.argv) != 3:
        print("Usage: python schedule_execute_process.py <path_to_program> <program_interval minutes>")
        logging.error("Invalid arguments. Usage: python schedule_execute_process.py <path_to_program> <program_interval minutes>")
        sys.exit(1)

    program_path = sys.argv[1]
    global EXEC_INTERVAL
    EXEC_INTERVAL = sys.argv[2]

    try:
        main(program_path)
    except Exception as e:
        logging.error(f"Process error: {e}", exc_info=True)
        sys.exit(1)
