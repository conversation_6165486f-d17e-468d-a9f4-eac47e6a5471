#!/usr/bin/env python3
"""
Simple test script for scheduler
"""

import time
import sys
from datetime import datetime

def main():
    print(f"Script started at: {datetime.now()}")
    print(f"Python version: {sys.version}")
    print(f"Arguments: {sys.argv}")
    
    # Simulate some work
    for i in range(3):
        print(f"Step {i+1}: Processing...")
        time.sleep(1)
        print(f"Step {i+1}: Completed")
    
    print(f"Script finished at: {datetime.now()}")
    return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("Script interrupted by user", file=sys.stderr)
        sys.exit(130)
    except Exception as e:
        print(f"Script error: {e}", file=sys.stderr)
        sys.exit(1)
