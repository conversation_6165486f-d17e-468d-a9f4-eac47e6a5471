#!/usr/bin/env python3
"""
实时输出演示脚本
这个脚本会产生持续的输出，用于测试调度器的实时显示功能
"""

import time
import sys
from datetime import datetime

def main():
    print(f"=== 实时输出演示开始 - {datetime.now()} ===")
    print(f"Python版本: {sys.version}")
    
    # 模拟一些需要时间的任务
    tasks = [
        "连接数据库",
        "读取配置文件", 
        "初始化系统组件",
        "加载数据",
        "处理业务逻辑",
        "生成报告",
        "发送通知",
        "清理资源"
    ]
    
    for i, task in enumerate(tasks, 1):
        print(f"[{i}/{len(tasks)}] 开始: {task}")
        
        # 模拟任务进度
        for progress in range(0, 101, 20):
            print(f"  进度: {progress}%")
            time.sleep(0.5)
        
        print(f"[{i}/{len(tasks)}] 完成: {task}")
        print()
    
    # 输出一些统计信息
    print("=== 执行统计 ===")
    print(f"总任务数: {len(tasks)}")
    print(f"执行时间: {len(tasks) * 3} 秒")
    print(f"完成时间: {datetime.now()}")
    
    print("=== 演示结束 ===")
    return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n脚本被用户中断", file=sys.stderr)
        sys.exit(130)
    except Exception as e:
        print(f"脚本执行出现异常: {e}", file=sys.stderr)
        sys.exit(1)
