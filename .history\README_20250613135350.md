# Python 脚本调度执行器

这是一个改进的Python脚本调度执行器，用于定期执行指定的Python脚本并管理其日志。

## 主要特性

### 🔧 核心功能
- **定期执行**：按指定时间间隔（分钟）执行目标Python脚本
- **进程监控**：实时监控被调用脚本的运行状态
- **优雅终止**：支持优雅的进程终止和强制杀死
- **信号处理**：响应SIGINT和SIGTERM信号进行优雅关闭

### 📝 日志管理
- **分离日志**：调度器日志和被调用脚本日志分别保存
- **日志轮转**：支持日志文件大小限制和自动轮转
- **输出捕获**：完整捕获被调用脚本的stdout和stderr
- **实时显示**：支持在终端实时显示脚本输出（默认启用）
- **静默模式**：可选择静默运行，只保存日志不显示输出
- **时间戳命名**：日志文件包含时间戳，便于追踪

### ⚙️ 配置管理
- **配置文件支持**：支持JSON格式的配置文件
- **命令行参数**：灵活的命令行参数配置
- **示例配置生成**：可自动生成示例配置文件

## 使用方法

### 基本用法

```bash
# 每60分钟执行一次 my_script.py（默认显示实时输出）
python schedule_execute_process_daily.py my_script.py 60

# 每24小时（1440分钟）执行一次，使用配置文件
python schedule_execute_process_daily.py my_script.py 1440 scheduler_config.json

# 静默模式运行（不显示实时输出）
python schedule_execute_process_daily.py my_script.py 60 --silent
```

### 生成配置文件

```bash
python schedule_execute_process_daily.py --create-config
```

这将创建一个 `scheduler_config.json` 文件，包含以下配置选项：

```json
{
  "check_interval": 60,
  "log_retention_days": 7,
  "max_log_size_mb": 10,
  "description": "Configuration file for schedule executor"
}
```

## 日志文件结构

```
logs/
├── scheduler_20231201.log          # 调度器日志
├── scheduler_20231201.log.1        # 轮转的调度器日志
├── my_script_20231201_143022.log   # 被调用脚本的输出日志
└── my_script_20231201_163022.log   # 下一次执行的日志
```

## 主要改进

### 1. 日志管理优化
- **问题**：原版本无法捕获被调用脚本的输出
- **解决**：使用subprocess.Popen的stdout/stderr重定向，将脚本输出保存到独立的日志文件
- **优势**：可以查看每次脚本执行的详细输出和错误信息

### 2. 进程管理改进
- **问题**：原版本进程终止逻辑简单，可能导致资源泄露
- **解决**：实现优雅终止机制，先尝试terminate()，超时后使用kill()
- **优势**：更安全的进程管理，避免僵尸进程

### 3. 代码结构重构
- **问题**：原版本使用全局变量，代码结构混乱
- **解决**：使用面向对象设计，封装调度器逻辑
- **优势**：代码更清晰，易于维护和扩展

### 4. 错误处理增强
- **问题**：原版本错误处理不够完善
- **解决**：添加详细的异常处理和日志记录
- **优势**：更好的错误诊断和恢复能力

### 5. 配置管理
- **问题**：原版本配置硬编码
- **解决**：支持配置文件和命令行参数
- **优势**：更灵活的配置管理

### 6. 实时输出显示 ✅
- **问题解决**：原版本无法在终端看到被调用脚本的实时输出
- **实现方案**：
  - **默认模式**：在终端实时显示脚本输出，同时保存到日志文件
  - **静默模式**：使用 `--silent` 参数，只保存日志不显示输出
  - **多线程处理**：使用独立线程监控脚本输出，不影响调度逻辑
- **使用场景**：
  - **开发调试**：实时查看脚本执行过程和输出
  - **生产环境**：使用静默模式减少终端输出干扰
  - **监控运维**：实时观察长时间运行脚本的状态

## 使用示例

### 示例1：监控数据处理脚本（实时输出）

```bash
# 每小时执行数据处理脚本，显示实时输出
python schedule_execute_process_daily.py data_processor.py 60
```

### 示例2：每日备份任务（静默模式）

```bash
# 每24小时执行备份脚本，静默运行
python schedule_execute_process_daily.py backup_script.py 1440 --silent
```

### 示例3：使用配置文件

```bash
# 首先生成配置文件
python schedule_execute_process_daily.py --create-config

# 编辑配置文件后使用
python schedule_execute_process_daily.py monitor_script.py 30 scheduler_config.json
```

### 示例4：实时监控脚本输出

```bash
# 每2分钟执行一次，实时查看脚本输出
python schedule_execute_process_daily.py realtime_demo.py 2
```

运行效果：
```
2025-06-13 13:49:21,921 - scheduler - INFO - Starting scheduler for script: realtime_demo.py
2025-06-13 13:49:21,922 - scheduler - INFO - Execution interval: 2 minutes
============================================================
SCRIPT OUTPUT START - realtime_demo.py
============================================================
=== 实时输出演示开始 - 2025-06-13 13:49:22.027625 ===
[1/8] 开始: 连接数据库
  进度: 0%
  进度: 20%
  进度: 40%
  ...
============================================================
SCRIPT OUTPUT END
============================================================
2025-06-13 13:50:21,945 - scheduler - INFO - Script completed successfully. Return code: 0
```

## 注意事项

1. **权限**：确保调度器有权限执行目标脚本
2. **路径**：使用绝对路径或确保脚本在正确的工作目录中
3. **资源**：监控系统资源使用情况，避免脚本执行时间过长
4. **日志清理**：定期清理旧的日志文件以节省磁盘空间
5. **实时输出**：
   - 默认启用实时输出显示，适合开发和调试
   - 生产环境建议使用 `--silent` 参数避免终端输出干扰
   - 实时输出和日志文件保存是同时进行的，不会影响日志记录

## 故障排除

### 常见问题

1. **脚本无法启动**
   - 检查脚本路径是否正确
   - 确认Python解释器路径
   - 查看调度器日志了解详细错误信息

2. **日志文件过大**
   - 调整配置文件中的max_log_size_mb参数
   - 设置合适的日志轮转策略

3. **进程无法终止**
   - 检查被调用脚本是否正确处理信号
   - 考虑在脚本中添加信号处理逻辑

4. **实时输出问题**
   - 如果看不到实时输出，检查是否使用了 `--silent` 参数
   - 某些脚本可能使用缓冲输出，导致延迟显示
   - 可以在脚本中使用 `sys.stdout.flush()` 强制刷新输出缓冲区
