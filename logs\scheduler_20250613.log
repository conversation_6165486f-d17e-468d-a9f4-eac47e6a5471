2025-06-13 13:48:18,700 - scheduler - INFO - Starting scheduler for script: realtime_demo.py
2025-06-13 13:48:18,701 - scheduler - INFO - Execution interval: 2 minutes
2025-06-13 13:48:18,712 - scheduler - INFO - Started script: realtime_demo.py with PID 8860
2025-06-13 13:48:18,712 - scheduler - INFO - Script output will be saved to: logs\realtime_demo_20250613_134818.log
2025-06-13 13:48:18,712 - scheduler - INFO - Real-time output enabled - script output will be shown below:
2025-06-13 13:49:21,921 - scheduler - INFO - Starting scheduler for script: realtime_demo.py
2025-06-13 13:49:21,922 - scheduler - INFO - Execution interval: 2 minutes
2025-06-13 13:49:21,940 - scheduler - INFO - Started script: realtime_demo.py with PID 26040
2025-06-13 13:49:21,940 - scheduler - INFO - Script output will be saved to: logs\realtime_demo_20250613_134921.log
2025-06-13 13:49:21,940 - scheduler - INFO - Real-time output enabled - script output will be shown below:
2025-06-13 13:50:21,945 - scheduler - INFO - Script completed successfully. Return code: 0
2025-06-13 13:50:21,945 - scheduler - INFO - Waiting for 60 seconds before next execution.
2025-06-13 13:50:52,229 - scheduler - INFO - Starting scheduler for script: simple_test.py
2025-06-13 13:50:52,229 - scheduler - INFO - Execution interval: 1 minutes
2025-06-13 13:50:52,243 - scheduler - INFO - Started script: simple_test.py with PID 27904
2025-06-13 13:50:52,243 - scheduler - INFO - Script output will be saved to: logs\simple_test_20250613_135052.log
2025-06-13 13:51:52,250 - scheduler - INFO - Script completed successfully. Return code: 0
2025-06-13 13:51:52,275 - scheduler - INFO - Started script: simple_test.py with PID 28388
2025-06-13 13:51:52,277 - scheduler - INFO - Script output will be saved to: logs\simple_test_20250613_135152.log
