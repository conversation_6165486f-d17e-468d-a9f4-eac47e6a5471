2025-06-12 16:45:37,925 - scheduler - INFO - Starting scheduler for script: example_script.py
2025-06-12 16:45:37,925 - scheduler - INFO - Execution interval: 1 minutes
2025-06-12 16:45:37,938 - scheduler - INFO - Started script: example_script.py with PID 31296
2025-06-12 16:45:37,939 - scheduler - INFO - <PERSON>ript output will be saved to: logs\example_script_20250612_164537.log
2025-06-12 16:46:37,950 - scheduler - ERROR - Script completed with error. Return code: 1
2025-06-12 16:46:37,966 - scheduler - INFO - Started script: example_script.py with PID 25436
2025-06-12 16:46:37,966 - scheduler - INFO - <PERSON>ript output will be saved to: logs\example_script_20250612_164637.log
2025-06-12 16:46:58,663 - scheduler - INFO - Received signal 2, shutting down gracefully...
2025-06-12 16:47:37,966 - scheduler - ERROR - <PERSON><PERSON><PERSON> completed with error. Return code: 1
2025-06-12 16:47:37,966 - scheduler - INFO - Scheduler stopped.
2025-06-12 16:47:47,479 - scheduler - INFO - Starting scheduler for script: example_script.py
2025-06-12 16:47:47,479 - scheduler - INFO - Execution interval: 1 minutes
2025-06-12 16:47:47,491 - scheduler - INFO - Started script: example_script.py with PID 30028
2025-06-12 16:47:47,491 - scheduler - INFO - Script output will be saved to: logs\example_script_20250612_164747.log
2025-06-12 16:48:52,741 - scheduler - INFO - Starting scheduler for script: simple_test.py
2025-06-12 16:48:52,741 - scheduler - INFO - Execution interval: 1 minutes
2025-06-12 16:48:52,754 - scheduler - INFO - Started script: simple_test.py with PID 31576
2025-06-12 16:48:52,754 - scheduler - INFO - Script output will be saved to: logs\simple_test_20250612_164852.log
2025-06-12 16:49:52,761 - scheduler - INFO - Script completed successfully. Return code: 0
2025-06-12 16:49:52,773 - scheduler - INFO - Started script: simple_test.py with PID 21460
2025-06-12 16:49:52,773 - scheduler - INFO - Script output will be saved to: logs\simple_test_20250612_164952.log
