#!/usr/bin/env python3
"""
示例脚本 - 用于演示调度器的日志捕获功能
这个脚本会产生各种类型的输出，包括正常输出、警告和错误
"""

import time
import sys
import random
from datetime import datetime

def main():
    print(f"=== 脚本开始执行 - {datetime.now()} ===")
    print(f"Python版本: {sys.version}")
    print(f"脚本参数: {sys.argv}")
    
    # 模拟一些工作
    tasks = [
        "初始化数据库连接",
        "读取配置文件", 
        "处理数据文件",
        "生成报告",
        "发送通知邮件",
        "清理临时文件"
    ]
    
    for i, task in enumerate(tasks, 1):
        print(f"[{i}/{len(tasks)}] 正在执行: {task}")
        
        # 随机模拟任务执行时间
        execution_time = random.uniform(1, 3)
        time.sleep(execution_time)
        
        # 随机产生一些警告或错误
        if random.random() < 0.2:  # 20% 概率产生警告
            print(f"警告: {task} 执行较慢，耗时 {execution_time:.2f} 秒", file=sys.stderr)
        elif random.random() < 0.1:  # 10% 概率产生错误
            print(f"错误: {task} 执行失败，但继续执行", file=sys.stderr)
        else:
            print(f"OK {task} 完成，耗时 {execution_time:.2f} 秒")
    
    # 模拟一些统计信息
    print("\n=== 执行统计 ===")
    print(f"总任务数: {len(tasks)}")
    print(f"成功任务数: {len(tasks) - 1}")  # 假设有一个失败
    print(f"执行时间: {sum(random.uniform(1, 3) for _ in tasks):.2f} 秒")
    
    # 随机决定脚本的退出状态
    if random.random() < 0.9:  # 90% 概率成功
        print(f"=== 脚本执行成功 - {datetime.now()} ===")
        return 0
    else:
        print("=== 脚本执行失败 ===", file=sys.stderr)
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n脚本被用户中断", file=sys.stderr)
        sys.exit(130)
    except Exception as e:
        print(f"脚本执行出现异常: {e}", file=sys.stderr)
        sys.exit(1)
