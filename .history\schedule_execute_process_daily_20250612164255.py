import subprocess
import sys
import time
import signal
import os
from datetime import datetime
from pathlib import Path
import logging
from logging.handlers import RotatingFileHandler
import json
import threading

class ScheduleExecutor:
    def __init__(self, program_path, exec_interval_minutes, config_file=None):
        self.program_path = program_path
        self.exec_interval_minutes = int(exec_interval_minutes)
        self.check_interval = 60  # 检查脚本状态的时间间隔（秒）
        self.current_process = None
        self.should_stop = False

        # 创建日志目录
        self.log_dir = Path("logs")
        self.log_dir.mkdir(exist_ok=True)

        # 设置日志
        self._setup_logging()

        # 加载配置
        if config_file and Path(config_file).exists():
            self._load_config(config_file)

        # 设置信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

    def _setup_logging(self):
        """设置日志配置"""
        # 调度器日志
        scheduler_log_file = self.log_dir / f"scheduler_{datetime.now().strftime('%Y%m%d')}.log"

        # 创建logger
        self.logger = logging.getLogger('scheduler')
        self.logger.setLevel(logging.INFO)

        # 创建文件处理器（带轮转）
        file_handler = RotatingFileHandler(
            scheduler_log_file,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5
        )
        file_handler.setLevel(logging.INFO)

        # 创建控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)

        # 创建格式器
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)

        # 添加处理器
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)

    def _load_config(self, config_file):
        """加载配置文件"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                self.check_interval = config.get('check_interval', self.check_interval)
                self.logger.info(f"Loaded configuration from {config_file}")
        except Exception as e:
            self.logger.warning(f"Failed to load config file {config_file}: {e}")

    def _signal_handler(self, signum, frame):
        """信号处理器"""
        self.logger.info(f"Received signal {signum}, shutting down gracefully...")
        self.should_stop = True

def run_script(program_path):
    try:
        proc = subprocess.Popen([sys.executable, program_path])
        logging.info(f"Started script: {program_path} with PID {proc.pid}")
        return proc
    except Exception as e:
        logging.error(f"Failed to start script: {program_path}. Error: {e}", exc_info=True)
        raise

def main(program_path):
    proc = None
    while True:
        start_time = datetime.now()

        if proc and proc.poll() is None:  # 如果上一个脚本还在运行
            logging.warning(f"Previous script is still running. Terminating it: {datetime.now()}")
            proc.terminate()
            time.sleep(5)
            if proc.poll() is None:
                proc.kill()  # 强制杀死进程
                logging.warning(f"Previous script killed: {datetime.now()}")

        proc = run_script(program_path)  # 启动新脚本
        program_interval = int(EXEC_INTERVAL) * 60
        while (datetime.now() - start_time).total_seconds() < program_interval:
            time.sleep(CHECK_INTERVAL)
            if proc.poll() is not None:  # 脚本已完成
                logging.info(f"Script completed: {datetime.now()}. Waiting for next execution.")
                # 计算剩余时间并等待
                remaining_time = program_interval - (datetime.now() - start_time).total_seconds()
                if remaining_time > 0:
                    logging.info(f"Waiting for {remaining_time} seconds before next execution.")
                    time.sleep(int(remaining_time))
                break

        # 这里没有 else，因为如果脚本在一天内完成，我们只需等待到下一个执行周期

if __name__ == '__main__':
    if len(sys.argv) != 3:
        print("Usage: python schedule_execute_process.py <path_to_program> <program_interval minutes>")
        logging.error("Invalid arguments. Usage: python schedule_execute_process.py <path_to_program> <program_interval minutes>")
        sys.exit(1)

    program_path = sys.argv[1]
    global EXEC_INTERVAL
    EXEC_INTERVAL = sys.argv[2]

    try:
        main(program_path)
    except Exception as e:
        logging.error(f"Process error: {e}", exc_info=True)
        sys.exit(1)
